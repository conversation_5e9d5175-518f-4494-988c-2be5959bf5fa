{"name": "unitybills", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@tailwindcss/postcss": "^4.1.12", "@tanstack/react-query": "^5.85.3", "autoprefixer": "^10.4.21", "axios": "^1.11.0", "framer-motion": "^12.23.12", "lucide-react": "^0.539.0", "postcss": "^8.5.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hot-toast": "^2.6.0", "react-router-dom": "^7.8.0", "tailwindcss": "^4.1.12", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.1.2"}}