import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Phone, ArrowRight, Check } from 'lucide-react';
import MobileLayout from '../layout/MobileLayout';
import { billsAPI } from '../../services/api';
import useWalletStore from '../../stores/walletStore';
import toast from 'react-hot-toast';

const AirtimeScreen = () => {
  const navigate = useNavigate();
  const { balance, formatBalance } = useWalletStore();
  
  const [formData, setFormData] = useState({
    provider: '',
    phoneNumber: '',
    amount: '',
  });
  const [providers, setProviders] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingProviders, setLoadingProviders] = useState(true);

  const quickAmounts = [100, 200, 500, 1000, 2000, 5000];

  useEffect(() => {
    fetchProviders();
  }, []);

  const fetchProviders = async () => {
    try {
      const response = await billsAPI.getAirtimeProviders();
      setProviders(response.data.providers || []);
    } catch (error) {
      console.error('Failed to fetch providers:', error);
      // Fallback providers
      setProviders([
        { id: 'mtn', name: 'MTN', logo: '/providers/mtn.png' },
        { id: 'glo', name: 'Glo', logo: '/providers/glo.png' },
        { id: 'airtel', name: 'Airtel', logo: '/providers/airtel.png' },
        { id: '9mobile', name: '9mobile', logo: '/providers/9mobile.png' },
      ]);
    } finally {
      setLoadingProviders(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleProviderSelect = (providerId) => {
    setFormData(prev => ({
      ...prev,
      provider: providerId
    }));
  };

  const handleQuickAmount = (amount) => {
    setFormData(prev => ({
      ...prev,
      amount: amount.toString()
    }));
  };

  const validateForm = () => {
    if (!formData.provider) {
      toast.error('Please select a network provider');
      return false;
    }
    if (!formData.phoneNumber) {
      toast.error('Please enter phone number');
      return false;
    }
    if (formData.phoneNumber.length < 11) {
      toast.error('Please enter a valid phone number');
      return false;
    }
    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      toast.error('Please enter a valid amount');
      return false;
    }
    if (parseFloat(formData.amount) > balance) {
      toast.error('Insufficient wallet balance');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);
    
    try {
      const response = await billsAPI.buyAirtime({
        provider: formData.provider,
        phone_number: formData.phoneNumber,
        amount: parseFloat(formData.amount),
      });

      toast.success('Airtime purchase successful!');
      navigate('/dashboard');
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Airtime purchase failed';
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <MobileLayout title="Buy Airtime">
      <div className="p-4 space-y-6">
        {/* Wallet Balance */}
        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Wallet Balance</p>
              <p className="text-lg font-semibold text-gray-900">{formatBalance()}</p>
            </div>
            <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
              <Phone className="w-6 h-6 text-primary-600" />
            </div>
          </div>
        </div>

        {/* Network Provider Selection */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Select Network</h3>
          {loadingProviders ? (
            <div className="grid grid-cols-2 gap-3">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="skeleton h-16 rounded-xl"></div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-2 gap-3">
              {providers.map((provider) => (
                <button
                  key={provider.id}
                  onClick={() => handleProviderSelect(provider.id)}
                  className={`p-4 rounded-xl border-2 transition-all ${
                    formData.provider === provider.id
                      ? 'border-primary-500 bg-primary-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center justify-center space-x-2">
                    <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                      <span className="text-xs font-medium">{provider.name.charAt(0)}</span>
                    </div>
                    <span className="font-medium text-gray-900">{provider.name}</span>
                    {formData.provider === provider.id && (
                      <Check className="w-4 h-4 text-primary-600" />
                    )}
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Phone Number Input */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Phone Number</h3>
          <div className="relative">
            <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="tel"
              name="phoneNumber"
              value={formData.phoneNumber}
              onChange={handleInputChange}
              className="input pl-10"
              placeholder="Enter phone number"
              maxLength="11"
            />
          </div>
        </div>

        {/* Amount Selection */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Amount</h3>
          
          {/* Quick Amount Buttons */}
          <div className="grid grid-cols-3 gap-2 mb-4">
            {quickAmounts.map((amount) => (
              <button
                key={amount}
                onClick={() => handleQuickAmount(amount)}
                className={`p-3 rounded-lg border transition-all ${
                  formData.amount === amount.toString()
                    ? 'border-primary-500 bg-primary-50 text-primary-600'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                ₦{amount.toLocaleString()}
              </button>
            ))}
          </div>

          {/* Custom Amount Input */}
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">₦</span>
            <input
              type="number"
              name="amount"
              value={formData.amount}
              onChange={handleInputChange}
              className="input pl-8"
              placeholder="Enter custom amount"
              min="50"
              max="50000"
            />
          </div>
        </div>

        {/* Purchase Button */}
        <button
          onClick={handleSubmit}
          disabled={isLoading || !formData.provider || !formData.phoneNumber || !formData.amount}
          className="btn-primary w-full flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
          ) : (
            <>
              <span>Buy Airtime</span>
              <ArrowRight className="w-4 h-4" />
            </>
          )}
        </button>
      </div>
    </MobileLayout>
  );
};

export default AirtimeScreen;
