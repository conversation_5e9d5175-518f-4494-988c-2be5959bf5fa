import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Globe, ArrowRight, Check, Phone } from 'lucide-react';
import MobileLayout from '../layout/MobileLayout';
import { billsAPI } from '../../services/api';
import useWalletStore from '../../stores/walletStore';
import toast from 'react-hot-toast';

const DataScreen = () => {
  const navigate = useNavigate();
  const { balance, formatBalance } = useWalletStore();
  
  const [formData, setFormData] = useState({
    provider: '',
    phoneNumber: '',
    planId: '',
  });
  const [providers, setProviders] = useState([]);
  const [dataPlans, setDataPlans] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingProviders, setLoadingProviders] = useState(true);
  const [loadingPlans, setLoadingPlans] = useState(false);

  useEffect(() => {
    fetchProviders();
  }, []);

  useEffect(() => {
    if (formData.provider) {
      fetchDataPlans(formData.provider);
    } else {
      setDataPlans([]);
    }
  }, [formData.provider]);

  const fetchProviders = async () => {
    try {
      // Using same providers as airtime for now
      setProviders([
        { id: 'mtn', name: 'MTN', logo: '/providers/mtn.png' },
        { id: 'glo', name: 'Glo', logo: '/providers/glo.png' },
        { id: 'airtel', name: 'Airtel', logo: '/providers/airtel.png' },
        { id: '9mobile', name: '9mobile', logo: '/providers/9mobile.png' },
      ]);
    } catch (error) {
      console.error('Failed to fetch providers:', error);
    } finally {
      setLoadingProviders(false);
    }
  };

  const fetchDataPlans = async (provider) => {
    setLoadingPlans(true);
    try {
      const response = await billsAPI.getDataPlans(provider);
      setDataPlans(response.data.plans || []);
    } catch (error) {
      console.error('Failed to fetch data plans:', error);
      // Fallback data plans
      setDataPlans([
        { id: '1gb_30', name: '1GB - 30 Days', price: 350, data: '1GB', validity: '30 days' },
        { id: '2gb_30', name: '2GB - 30 Days', price: 700, data: '2GB', validity: '30 days' },
        { id: '5gb_30', name: '5GB - 30 Days', price: 1500, data: '5GB', validity: '30 days' },
        { id: '10gb_30', name: '10GB - 30 Days', price: 2500, data: '10GB', validity: '30 days' },
        { id: '20gb_30', name: '20GB - 30 Days', price: 4000, data: '20GB', validity: '30 days' },
      ]);
    } finally {
      setLoadingPlans(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleProviderSelect = (providerId) => {
    setFormData(prev => ({
      ...prev,
      provider: providerId,
      planId: '' // Reset plan selection when provider changes
    }));
  };

  const handlePlanSelect = (planId) => {
    setFormData(prev => ({
      ...prev,
      planId
    }));
  };

  const getSelectedPlan = () => {
    return dataPlans.find(plan => plan.id === formData.planId);
  };

  const validateForm = () => {
    if (!formData.provider) {
      toast.error('Please select a network provider');
      return false;
    }
    if (!formData.phoneNumber) {
      toast.error('Please enter phone number');
      return false;
    }
    if (formData.phoneNumber.length < 11) {
      toast.error('Please enter a valid phone number');
      return false;
    }
    if (!formData.planId) {
      toast.error('Please select a data plan');
      return false;
    }
    
    const selectedPlan = getSelectedPlan();
    if (selectedPlan && selectedPlan.price > balance) {
      toast.error('Insufficient wallet balance');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);
    
    try {
      const response = await billsAPI.buyData({
        provider: formData.provider,
        phone_number: formData.phoneNumber,
        plan_id: formData.planId,
      });

      toast.success('Data purchase successful!');
      navigate('/dashboard');
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Data purchase failed';
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const selectedPlan = getSelectedPlan();

  return (
    <MobileLayout title="Buy Data">
      <div className="p-4 space-y-6">
        {/* Wallet Balance */}
        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Wallet Balance</p>
              <p className="text-lg font-semibold text-gray-900">{formatBalance()}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
              <Globe className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        {/* Network Provider Selection */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Select Network</h3>
          {loadingProviders ? (
            <div className="grid grid-cols-2 gap-3">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="skeleton h-16 rounded-xl"></div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-2 gap-3">
              {providers.map((provider) => (
                <button
                  key={provider.id}
                  onClick={() => handleProviderSelect(provider.id)}
                  className={`p-4 rounded-xl border-2 transition-all ${
                    formData.provider === provider.id
                      ? 'border-primary-500 bg-primary-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center justify-center space-x-2">
                    <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                      <span className="text-xs font-medium">{provider.name.charAt(0)}</span>
                    </div>
                    <span className="font-medium text-gray-900">{provider.name}</span>
                    {formData.provider === provider.id && (
                      <Check className="w-4 h-4 text-primary-600" />
                    )}
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Phone Number Input */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Phone Number</h3>
          <div className="relative">
            <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="tel"
              name="phoneNumber"
              value={formData.phoneNumber}
              onChange={handleInputChange}
              className="input pl-10"
              placeholder="Enter phone number"
              maxLength="11"
            />
          </div>
        </div>

        {/* Data Plans */}
        {formData.provider && (
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Select Data Plan</h3>
            {loadingPlans ? (
              <div className="space-y-3">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="skeleton h-16 rounded-xl"></div>
                ))}
              </div>
            ) : (
              <div className="space-y-3">
                {dataPlans.map((plan) => (
                  <button
                    key={plan.id}
                    onClick={() => handlePlanSelect(plan.id)}
                    className={`w-full p-4 rounded-xl border-2 transition-all text-left ${
                      formData.planId === plan.id
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-900">{plan.data}</p>
                        <p className="text-sm text-gray-600">{plan.validity}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-gray-900">₦{plan.price.toLocaleString()}</p>
                        {formData.planId === plan.id && (
                          <Check className="w-4 h-4 text-primary-600 ml-auto mt-1" />
                        )}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Purchase Summary */}
        {selectedPlan && (
          <div className="card bg-primary-50 border-primary-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Purchase Summary</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Network:</span>
                <span className="font-medium">{providers.find(p => p.id === formData.provider)?.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Phone:</span>
                <span className="font-medium">{formData.phoneNumber}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Data Plan:</span>
                <span className="font-medium">{selectedPlan.data}</span>
              </div>
              <div className="flex justify-between border-t border-primary-200 pt-2">
                <span className="text-gray-900 font-semibold">Total:</span>
                <span className="font-bold text-primary-600">₦{selectedPlan.price.toLocaleString()}</span>
              </div>
            </div>
          </div>
        )}

        {/* Purchase Button */}
        <button
          onClick={handleSubmit}
          disabled={isLoading || !formData.provider || !formData.phoneNumber || !formData.planId}
          className="btn-primary w-full flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
          ) : (
            <>
              <span>Buy Data</span>
              <ArrowRight className="w-4 h-4" />
            </>
          )}
        </button>
      </div>
    </MobileLayout>
  );
};

export default DataScreen;
