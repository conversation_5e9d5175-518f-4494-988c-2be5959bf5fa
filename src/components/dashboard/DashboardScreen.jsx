import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Plus, 
  RefreshCw, 
  Eye, 
  EyeOff,
  Phone,
  Globe,
  Tv,
  Zap,
  GraduationCap,
  Shield,
  MoreHorizontal,
  Gift,
  Grid3X3
} from 'lucide-react';
import MobileLayout from '../layout/MobileLayout';
import useAuthStore from '../../stores/authStore';
import useWalletStore from '../../stores/walletStore';

const DashboardScreen = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const { balance, isLoading, fetchBalance, formatBalance } = useWalletStore();
  const [showBalance, setShowBalance] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchBalance();
  }, [fetchBalance]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchBalance();
    setRefreshing(false);
  };

  const services = [
    {
      id: 'airtime',
      title: 'Buy Phone Airtime',
      icon: Phone,
      color: 'bg-orange-100 text-orange-600',
      path: '/airtime'
    },
    {
      id: 'data',
      title: 'Buy Internet Data',
      icon: Globe,
      color: 'bg-green-100 text-green-600',
      path: '/data'
    },
    {
      id: 'tv',
      title: 'Pay TV Subscription',
      icon: Tv,
      color: 'bg-yellow-100 text-yellow-600',
      path: '/tv-subscription'
    },
    {
      id: 'electricity',
      title: 'Pay Electricity Bills',
      icon: Zap,
      color: 'bg-pink-100 text-pink-600',
      path: '/electricity'
    },
    {
      id: 'education',
      title: 'Education Payments',
      icon: GraduationCap,
      color: 'bg-purple-100 text-purple-600',
      path: '/education'
    },
    {
      id: 'insurance',
      title: 'Buy Insurance',
      icon: Shield,
      color: 'bg-blue-100 text-blue-600',
      path: '/insurance'
    },
    {
      id: 'other',
      title: 'Other Services/ Merchants',
      icon: MoreHorizontal,
      color: 'bg-gray-100 text-gray-600',
      path: '/other-services'
    },
    {
      id: 'rewards',
      title: 'Rewards & Offers',
      icon: Gift,
      color: 'bg-emerald-100 text-emerald-600',
      path: '/rewards'
    },
    {
      id: 'all-products',
      title: 'All Products',
      icon: Grid3X3,
      color: 'bg-primary-100 text-primary-600',
      path: '/products'
    }
  ];

  return (
    <MobileLayout title="">
      <div className="space-y-6 pb-6">
        {/* Wallet Balance Card */}
        <div className="wallet-card mx-4 mt-4">
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-primary-100 text-sm mb-1">Wallet Balance</p>
              <div className="flex items-center space-x-2">
                <h2 className="text-2xl font-bold">
                  {showBalance ? formatBalance() : '₦••••••'}
                </h2>
                <button
                  onClick={() => setShowBalance(!showBalance)}
                  className="p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors"
                >
                  {showBalance ? (
                    <EyeOff className="w-4 h-4" />
                  ) : (
                    <Eye className="w-4 h-4" />
                  )}
                </button>
                <button
                  onClick={handleRefresh}
                  disabled={refreshing}
                  className="p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors"
                >
                  <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
                </button>
              </div>
            </div>
            <button
              onClick={() => navigate('/fund-wallet')}
              className="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-xl flex items-center space-x-2 transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span className="text-sm font-medium">Add Fund</span>
            </button>
          </div>
        </div>

        {/* Cashback & VTPoint Section */}
        <div className="px-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-orange-50 rounded-xl p-4 flex items-center space-x-3">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <Gift className="w-5 h-5 text-orange-600" />
              </div>
              <div>
                <p className="text-gray-600 text-sm">Cashback</p>
                <p className="font-semibold text-gray-900">₦0.00</p>
              </div>
            </div>
            <div className="bg-primary-50 rounded-xl p-4 flex items-center justify-between">
              <div>
                <p className="text-gray-600 text-sm">VTPoint</p>
                <p className="font-semibold text-gray-900">0</p>
              </div>
              <button className="bg-red-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                Earn Now
              </button>
            </div>
          </div>
        </div>

        {/* Our Services Section */}
        <div className="px-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Our Services</h3>
          <div className="service-grid">
            {services.map((service) => {
              const Icon = service.icon;
              return (
                <button
                  key={service.id}
                  onClick={() => navigate(service.path)}
                  className="service-card group"
                >
                  <div className={`w-12 h-12 rounded-xl ${service.color} flex items-center justify-center mb-3 group-hover:scale-110 transition-transform`}>
                    <Icon className="w-6 h-6" />
                  </div>
                  <p className="text-xs font-medium text-gray-700 text-center leading-tight">
                    {service.title}
                  </p>
                </button>
              );
            })}
          </div>
        </div>

        {/* Promotional Banner */}
        <div className="px-4">
          <div className="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl p-6 text-white relative overflow-hidden">
            <div className="relative z-10">
              <h3 className="text-lg font-bold mb-2">WAEC Second Series</h3>
              <p className="text-sm mb-4 opacity-90">Registration Is Now Open!</p>
              <button className="bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                Click Here To Get Your PIN
              </button>
            </div>
            {/* Background decoration */}
            <div className="absolute right-0 top-0 w-32 h-32 bg-white bg-opacity-10 rounded-full -translate-y-8 translate-x-8"></div>
            <div className="absolute right-8 bottom-0 w-20 h-20 bg-white bg-opacity-10 rounded-full translate-y-4"></div>
          </div>
        </div>

        {/* To Do Lists Section */}
        <div className="px-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">To Do Lists</h3>
          <div className="card">
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                <Grid3X3 className="w-8 h-8 text-gray-400" />
              </div>
              <p className="text-gray-500">No pending tasks</p>
              <p className="text-sm text-gray-400 mt-1">You're all caught up!</p>
            </div>
          </div>
        </div>
      </div>
    </MobileLayout>
  );
};

export default DashboardScreen;
