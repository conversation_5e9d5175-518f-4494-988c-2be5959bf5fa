import { useState } from 'react';
import { 
  MessageCircle, 
  Phone, 
  Mail, 
  ChevronDown, 
  ChevronRight,
  Search
} from 'lucide-react';
import MobileLayout from '../layout/MobileLayout';

const HelpScreen = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFaq, setExpandedFaq] = useState(null);

  const contactMethods = [
    {
      id: 'chat',
      title: 'Live Chat',
      description: 'Chat with our support team',
      icon: MessageCircle,
      color: 'text-blue-600 bg-blue-100',
      action: 'Start Chat',
    },
    {
      id: 'phone',
      title: 'Call Us',
      description: '+234 ************',
      icon: Phone,
      color: 'text-green-600 bg-green-100',
      action: 'Call Now',
    },
    {
      id: 'email',
      title: 'Email Support',
      description: '<EMAIL>',
      icon: Mail,
      color: 'text-purple-600 bg-purple-100',
      action: 'Send Email',
    },
  ];

  const faqs = [
    {
      id: 1,
      question: 'How do I fund my wallet?',
      answer: 'You can fund your wallet using debit/credit cards or bank transfer. Go to Fund Wallet, select your preferred payment method, enter the amount, and follow the instructions.',
    },
    {
      id: 2,
      question: 'Why did my transaction fail?',
      answer: 'Transactions can fail due to insufficient wallet balance, network issues, or incorrect details. Check your wallet balance and ensure all details are correct before retrying.',
    },
    {
      id: 3,
      question: 'How long does it take to receive airtime/data?',
      answer: 'Airtime and data are usually delivered instantly. In rare cases, it may take up to 5 minutes. If you don\'t receive it after 5 minutes, contact support.',
    },
    {
      id: 4,
      question: 'Can I get a refund for failed transactions?',
      answer: 'Yes, failed transactions are automatically refunded to your wallet within 24 hours. If you don\'t receive a refund, please contact our support team.',
    },
    {
      id: 5,
      question: 'How do I change my account details?',
      answer: 'Go to More > My Account to update your profile information. Some changes may require verification for security purposes.',
    },
    {
      id: 6,
      question: 'Is my payment information secure?',
      answer: 'Yes, we use industry-standard encryption to protect your payment information. We never store your card details on our servers.',
    },
  ];

  const filteredFaqs = faqs.filter(faq =>
    faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const toggleFaq = (faqId) => {
    setExpandedFaq(expandedFaq === faqId ? null : faqId);
  };

  return (
    <MobileLayout title="Help & Support">
      <div className="p-4 space-y-6">
        {/* Contact Methods */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact Us</h3>
          <div className="space-y-3">
            {contactMethods.map((method) => {
              const Icon = method.icon;
              return (
                <button
                  key={method.id}
                  className="w-full flex items-center space-x-4 p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors text-left"
                >
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center ${method.color}`}>
                    <Icon className="w-6 h-6" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">{method.title}</p>
                    <p className="text-sm text-gray-600">{method.description}</p>
                  </div>
                  <div className="text-primary-600 text-sm font-medium">
                    {method.action}
                  </div>
                </button>
              );
            })}
          </div>
        </div>

        {/* FAQ Section */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Frequently Asked Questions</h3>
          
          {/* Search */}
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="input pl-10"
              placeholder="Search FAQs..."
            />
          </div>

          {/* FAQ List */}
          <div className="space-y-3">
            {filteredFaqs.map((faq) => (
              <div key={faq.id} className="border border-gray-200 rounded-lg">
                <button
                  onClick={() => toggleFaq(faq.id)}
                  className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 transition-colors"
                >
                  <span className="font-medium text-gray-900 pr-4">{faq.question}</span>
                  {expandedFaq === faq.id ? (
                    <ChevronDown className="w-5 h-5 text-gray-400 flex-shrink-0" />
                  ) : (
                    <ChevronRight className="w-5 h-5 text-gray-400 flex-shrink-0" />
                  )}
                </button>
                {expandedFaq === faq.id && (
                  <div className="px-4 pb-4">
                    <p className="text-gray-600 text-sm leading-relaxed">{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>

          {filteredFaqs.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">No FAQs found matching your search.</p>
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 gap-3">
            <button className="p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors text-center">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <MessageCircle className="w-4 h-4 text-blue-600" />
              </div>
              <span className="text-sm font-medium text-gray-900">Report Issue</span>
            </button>
            <button className="p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors text-center">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <Phone className="w-4 h-4 text-green-600" />
              </div>
              <span className="text-sm font-medium text-gray-900">Request Callback</span>
            </button>
          </div>
        </div>

        {/* Operating Hours */}
        <div className="card bg-blue-50 border-blue-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Support Hours</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Monday - Friday:</span>
              <span className="font-medium">8:00 AM - 8:00 PM</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Saturday:</span>
              <span className="font-medium">9:00 AM - 5:00 PM</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Sunday:</span>
              <span className="font-medium">10:00 AM - 4:00 PM</span>
            </div>
          </div>
        </div>
      </div>
    </MobileLayout>
  );
};

export default HelpScreen;
