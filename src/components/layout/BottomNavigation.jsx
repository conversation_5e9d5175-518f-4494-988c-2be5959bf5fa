import { useLocation, useNavigate } from 'react-router-dom';
import { 
  Home, 
  CreditCard, 
  HelpCircle, 
  MoreHorizontal,
  Wallet
} from 'lucide-react';

const BottomNavigation = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const navItems = [
    {
      id: 'home',
      label: 'Home',
      icon: Home,
      path: '/dashboard',
    },
    {
      id: 'wallet',
      label: 'Fund Wallet',
      icon: Wallet,
      path: '/fund-wallet',
    },
    {
      id: 'help',
      label: 'Help',
      icon: HelpCircle,
      path: '/help',
    },
    {
      id: 'more',
      label: 'More',
      icon: MoreHorizontal,
      path: '/more',
    },
  ];

  const isActive = (path) => {
    if (path === '/dashboard') {
      return location.pathname === '/dashboard' || location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  const handleNavigation = (path) => {
    navigate(path);
  };

  return (
    <div className="bottom-nav safe-area-bottom">
      <div className="flex justify-around items-center">
        {navItems.map((item) => {
          const Icon = item.icon;
          const active = isActive(item.path);
          
          return (
            <button
              key={item.id}
              onClick={() => handleNavigation(item.path)}
              className={`nav-item ${active ? 'active' : 'text-gray-500 hover:text-gray-700'}`}
            >
              <Icon className="w-5 h-5 mb-1" />
              <span className="text-xs font-medium">{item.label}</span>
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default BottomNavigation;
