import MobileHeader from './MobileHeader';
import BottomNavigation from './BottomNavigation';

const MobileLayout = ({ 
  children, 
  title, 
  showHeader = true, 
  showBottomNav = true,
  showProfile = true,
  showNotifications = true,
  className = ''
}) => {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      {showHeader && (
        <MobileHeader 
          title={title}
          showProfile={showProfile}
          showNotifications={showNotifications}
        />
      )}

      {/* Main Content */}
      <main className={`flex-1 overflow-y-auto ${showBottomNav ? 'pb-20' : ''} ${className}`}>
        <div className="container mx-auto">
          {children}
        </div>
      </main>

      {/* Bottom Navigation */}
      {showBottomNav && <BottomNavigation />}
    </div>
  );
};

export default MobileLayout;
