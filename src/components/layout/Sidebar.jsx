import { useNavigate } from 'react-router-dom';
import { 
  X, 
  Home, 
  Phone, 
  Globe, 
  Tv, 
  Zap, 
  GraduationCap,
  Shield,
  MoreHorizontal,
  Gift,
  Wallet,
  Receipt,
  TrendingUp,
  User,
  LogOut
} from 'lucide-react';
import useAuthStore from '../../stores/authStore';

const Sidebar = ({ isOpen, onClose }) => {
  const navigate = useNavigate();
  const { user, logout } = useAuthStore();

  const menuItems = [
    {
      id: 'home',
      label: 'Home',
      icon: Home,
      path: '/dashboard',
    },
    {
      id: 'airtime',
      label: 'Airtime',
      icon: Phone,
      path: '/airtime',
    },
    {
      id: 'data',
      label: 'Data',
      icon: Globe,
      path: '/data',
    },
    {
      id: 'tv',
      label: 'Tv Subscription',
      icon: Tv,
      path: '/tv-subscription',
    },
    {
      id: 'electricity',
      label: 'Electricity',
      icon: Zap,
      path: '/electricity',
    },
    {
      id: 'education',
      label: 'Education Payments',
      icon: GraduationCap,
      path: '/education',
    },
    {
      id: 'insurance',
      label: 'Buy Insurance',
      icon: Shield,
      path: '/insurance',
    },
    {
      id: 'all-products',
      label: 'All Products',
      icon: MoreHorizontal,
      path: '/products',
    },
    {
      id: 'refer',
      label: 'Refer And Earn',
      icon: Gift,
      path: '/refer',
    },
    {
      id: 'fund-wallet',
      label: 'Fund Wallet',
      icon: Wallet,
      path: '/fund-wallet',
    },
    {
      id: 'transactions',
      label: 'Transactions',
      icon: Receipt,
      path: '/transactions',
    },
    {
      id: 'earnings',
      label: 'My Earnings',
      icon: TrendingUp,
      path: '/earnings',
    },
    {
      id: 'account',
      label: 'My Account',
      icon: User,
      path: '/account',
    },
  ];

  const handleNavigation = (path) => {
    navigate(path);
    onClose();
  };

  const handleLogout = async () => {
    await logout();
    navigate('/login');
    onClose();
  };

  const getInitials = (firstName, lastName) => {
    return `${firstName?.charAt(0) || ''}${lastName?.charAt(0) || ''}`.toUpperCase();
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        onClick={onClose}
      />
      
      {/* Sidebar */}
      <div className="fixed left-0 top-0 bottom-0 w-80 bg-white z-50 transform transition-transform duration-300 ease-in-out">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="bg-primary-600 text-white p-4 safe-area-top">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                  {user?.avatar ? (
                    <img
                      src={user.avatar}
                      alt={`${user.first_name} ${user.last_name}`}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                  ) : (
                    <span className="text-primary-600 text-sm font-medium">
                      {getInitials(user?.first_name, user?.last_name)}
                    </span>
                  )}
                </div>
                <div>
                  <h3 className="font-semibold">
                    {user?.first_name} {user?.last_name}
                  </h3>
                  <p className="text-primary-100 text-sm">{user?.email}</p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="p-1 rounded-lg hover:bg-white hover:bg-opacity-20 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
          </div>

          {/* Menu Items */}
          <div className="flex-1 overflow-y-auto py-4">
            <nav className="px-4 space-y-1">
              {menuItems.map((item) => {
                const Icon = item.icon;
                return (
                  <button
                    key={item.id}
                    onClick={() => handleNavigation(item.path)}
                    className="w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors text-left"
                  >
                    <Icon className="w-5 h-5 text-gray-500" />
                    <span className="font-medium">{item.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4 safe-area-bottom">
            <button
              onClick={handleLogout}
              className="w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-red-600 hover:bg-red-50 transition-colors"
            >
              <LogOut className="w-5 h-5" />
              <span className="font-medium">Logout</span>
            </button>
            
            <div className="mt-4 text-center">
              <p className="text-gray-500 text-sm">Version 1.0.0</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
