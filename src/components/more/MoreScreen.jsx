import { useNavigate } from 'react-router-dom';
import { 
  User, 
  Receipt, 
  TrendingUp, 
  Gift, 
  HelpCircle, 
  Settings, 
  LogOut,
  ChevronRight
} from 'lucide-react';
import MobileLayout from '../layout/MobileLayout';
import useAuthStore from '../../stores/authStore';

const MoreScreen = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuthStore();

  const menuItems = [
    {
      id: 'account',
      title: 'My Account',
      icon: User,
      path: '/account',
      color: 'text-blue-600 bg-blue-100',
    },
    {
      id: 'transactions',
      title: 'Transaction History',
      icon: Receipt,
      path: '/transactions',
      color: 'text-green-600 bg-green-100',
    },
    {
      id: 'earnings',
      title: 'My Earnings',
      icon: TrendingUp,
      path: '/earnings',
      color: 'text-purple-600 bg-purple-100',
    },
    {
      id: 'refer',
      title: '<PERSON><PERSON> & <PERSON>arn',
      icon: Gift,
      path: '/refer',
      color: 'text-orange-600 bg-orange-100',
    },
    {
      id: 'help',
      title: 'Help & Support',
      icon: HelpCircle,
      path: '/help',
      color: 'text-yellow-600 bg-yellow-100',
    },
    {
      id: 'settings',
      title: 'Settings',
      icon: Settings,
      path: '/settings',
      color: 'text-gray-600 bg-gray-100',
    },
  ];

  const handleNavigation = (path) => {
    navigate(path);
  };

  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };

  const getInitials = (firstName, lastName) => {
    return `${firstName?.charAt(0) || ''}${lastName?.charAt(0) || ''}`.toUpperCase();
  };

  return (
    <MobileLayout title="More">
      <div className="p-4 space-y-6">
        {/* User Profile Card */}
        <div className="card">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center">
              {user?.avatar ? (
                <img
                  src={user.avatar}
                  alt={`${user.first_name} ${user.last_name}`}
                  className="w-16 h-16 rounded-full object-cover"
                />
              ) : (
                <span className="text-white text-xl font-medium">
                  {getInitials(user?.first_name, user?.last_name)}
                </span>
              )}
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900">
                {user?.first_name} {user?.last_name}
              </h3>
              <p className="text-gray-600">{user?.email}</p>
              <p className="text-sm text-primary-600 font-medium">{user?.phone}</p>
            </div>
            <button
              onClick={() => navigate('/account')}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <ChevronRight className="w-5 h-5 text-gray-400" />
            </button>
          </div>
        </div>

        {/* Menu Items */}
        <div className="card">
          <div className="space-y-1">
            {menuItems.map((item, index) => {
              const Icon = item.icon;
              return (
                <button
                  key={item.id}
                  onClick={() => handleNavigation(item.path)}
                  className="w-full flex items-center space-x-4 p-4 rounded-lg hover:bg-gray-50 transition-colors text-left"
                >
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${item.color}`}>
                    <Icon className="w-5 h-5" />
                  </div>
                  <span className="flex-1 font-medium text-gray-900">{item.title}</span>
                  <ChevronRight className="w-5 h-5 text-gray-400" />
                </button>
              );
            })}
          </div>
        </div>

        {/* Logout Button */}
        <div className="card">
          <button
            onClick={handleLogout}
            className="w-full flex items-center space-x-4 p-4 rounded-lg hover:bg-red-50 transition-colors text-left text-red-600"
          >
            <div className="w-10 h-10 rounded-full flex items-center justify-center bg-red-100">
              <LogOut className="w-5 h-5" />
            </div>
            <span className="flex-1 font-medium">Logout</span>
            <ChevronRight className="w-5 h-5 text-red-400" />
          </button>
        </div>

        {/* App Info */}
        <div className="text-center py-4">
          <p className="text-gray-500 text-sm">UnityBills v1.0.0</p>
          <p className="text-gray-400 text-xs mt-1">© 2024 UnityBills. All rights reserved.</p>
        </div>
      </div>
    </MobileLayout>
  );
};

export default MoreScreen;
