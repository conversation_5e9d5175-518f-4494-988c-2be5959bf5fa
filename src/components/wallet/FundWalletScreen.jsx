import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Wallet, CreditCard, ArrowRight, Plus } from 'lucide-react';
import MobileLayout from '../layout/MobileLayout';
import useWalletStore from '../../stores/walletStore';
import toast from 'react-hot-toast';

const FundWalletScreen = () => {
  const navigate = useNavigate();
  const { balance, formatBalance, fundWallet, isLoading } = useWalletStore();
  
  const [formData, setFormData] = useState({
    amount: '',
    paymentMethod: 'card',
  });

  const quickAmounts = [1000, 2000, 5000, 10000, 20000, 50000];

  const paymentMethods = [
    {
      id: 'card',
      name: 'Debit/Credit Card',
      icon: CreditCard,
      description: 'Pay with your bank card',
    },
    {
      id: 'bank_transfer',
      name: 'Bank Transfer',
      icon: Wallet,
      description: 'Transfer from your bank account',
    },
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleQuickAmount = (amount) => {
    setFormData(prev => ({
      ...prev,
      amount: amount.toString()
    }));
  };

  const handlePaymentMethodSelect = (methodId) => {
    setFormData(prev => ({
      ...prev,
      paymentMethod: methodId
    }));
  };

  const validateForm = () => {
    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      toast.error('Please enter a valid amount');
      return false;
    }
    if (parseFloat(formData.amount) < 100) {
      toast.error('Minimum funding amount is ₦100');
      return false;
    }
    if (parseFloat(formData.amount) > 500000) {
      toast.error('Maximum funding amount is ₦500,000');
      return false;
    }
    if (!formData.paymentMethod) {
      toast.error('Please select a payment method');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    const result = await fundWallet({
      amount: parseFloat(formData.amount),
      payment_method: formData.paymentMethod,
    });

    if (result.success) {
      // In a real app, this would redirect to payment gateway
      toast.success('Redirecting to payment gateway...');
      setTimeout(() => {
        navigate('/dashboard');
      }, 2000);
    }
  };

  return (
    <MobileLayout title="Fund Wallet">
      <div className="p-4 space-y-6">
        {/* Current Balance */}
        <div className="wallet-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-primary-100 text-sm mb-1">Current Balance</p>
              <h2 className="text-2xl font-bold">{formatBalance()}</h2>
            </div>
            <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <Wallet className="w-6 h-6" />
            </div>
          </div>
        </div>

        {/* Quick Amount Selection */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Amount</h3>
          <div className="grid grid-cols-3 gap-3">
            {quickAmounts.map((amount) => (
              <button
                key={amount}
                onClick={() => handleQuickAmount(amount)}
                className={`p-3 rounded-xl border-2 transition-all ${
                  formData.amount === amount.toString()
                    ? 'border-primary-500 bg-primary-50 text-primary-600'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="text-center">
                  <Plus className="w-4 h-4 mx-auto mb-1" />
                  <span className="text-sm font-medium">₦{amount.toLocaleString()}</span>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Custom Amount Input */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Enter Amount</h3>
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-lg">₦</span>
            <input
              type="number"
              name="amount"
              value={formData.amount}
              onChange={handleInputChange}
              className="input pl-8 text-lg font-semibold"
              placeholder="0.00"
              min="100"
              max="500000"
            />
          </div>
          <p className="text-sm text-gray-500 mt-2">
            Minimum: ₦100 • Maximum: ₦500,000
          </p>
        </div>

        {/* Payment Method Selection */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Method</h3>
          <div className="space-y-3">
            {paymentMethods.map((method) => {
              const Icon = method.icon;
              return (
                <button
                  key={method.id}
                  onClick={() => handlePaymentMethodSelect(method.id)}
                  className={`w-full p-4 rounded-xl border-2 transition-all text-left ${
                    formData.paymentMethod === method.id
                      ? 'border-primary-500 bg-primary-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                      formData.paymentMethod === method.id
                        ? 'bg-primary-100 text-primary-600'
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      <Icon className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">{method.name}</p>
                      <p className="text-sm text-gray-600">{method.description}</p>
                    </div>
                    {formData.paymentMethod === method.id && (
                      <div className="w-5 h-5 bg-primary-600 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                      </div>
                    )}
                  </div>
                </button>
              );
            })}
          </div>
        </div>

        {/* Transaction Summary */}
        {formData.amount && parseFloat(formData.amount) > 0 && (
          <div className="card bg-green-50 border-green-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Transaction Summary</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Amount to fund:</span>
                <span className="font-medium">₦{parseFloat(formData.amount).toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Transaction fee:</span>
                <span className="font-medium">₦0.00</span>
              </div>
              <div className="flex justify-between border-t border-green-200 pt-2">
                <span className="text-gray-900 font-semibold">Total to pay:</span>
                <span className="font-bold text-green-600">₦{parseFloat(formData.amount).toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">New balance:</span>
                <span className="font-medium">₦{(balance + parseFloat(formData.amount)).toLocaleString()}</span>
              </div>
            </div>
          </div>
        )}

        {/* Fund Button */}
        <button
          onClick={handleSubmit}
          disabled={isLoading || !formData.amount || !formData.paymentMethod}
          className="btn-primary w-full flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
          ) : (
            <>
              <span>Proceed to Payment</span>
              <ArrowRight className="w-4 h-4" />
            </>
          )}
        </button>

        {/* Security Notice */}
        <div className="card bg-blue-50 border-blue-200">
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg className="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <p className="text-sm font-medium text-blue-900">Secure Payment</p>
              <p className="text-sm text-blue-700 mt-1">
                Your payment information is encrypted and secure. We never store your card details.
              </p>
            </div>
          </div>
        </div>
      </div>
    </MobileLayout>
  );
};

export default FundWalletScreen;
