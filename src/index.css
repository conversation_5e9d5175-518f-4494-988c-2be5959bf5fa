@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-gray-50 font-sans text-gray-900 antialiased;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    overflow-x: hidden;
  }

  html {
    @apply scroll-smooth;
  }

  /* Mobile-first responsive design */
  .container {
    @apply w-full max-w-sm mx-auto px-4;
  }

  /* Custom scrollbar for mobile */
  ::-webkit-scrollbar {
    width: 4px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
}

@layer components {
  /* Mobile-first button styles */
  .btn {
    @apply px-4 py-3 rounded-xl font-medium text-center transition-all duration-200 active:scale-95 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-lg;
  }

  .btn-secondary {
    @apply btn bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500;
  }

  .btn-outline {
    @apply btn border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white focus:ring-primary-500;
  }

  /* Card components */
  .card {
    @apply bg-white rounded-2xl shadow-card p-4 border border-gray-100;
  }

  .card-compact {
    @apply bg-white rounded-xl shadow-mobile p-3 border border-gray-50;
  }

  /* Input styles */
  .input {
    @apply w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-primary-500 focus:ring-2 focus:ring-primary-100 transition-colors duration-200 text-base;
  }

  /* Mobile navigation */
  .bottom-nav {
    @apply fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2 z-50;
  }

  .nav-item {
    @apply flex flex-col items-center justify-center p-2 rounded-lg transition-colors duration-200;
  }

  .nav-item.active {
    @apply text-primary-600 bg-primary-50;
  }

  /* Service grid */
  .service-grid {
    @apply grid grid-cols-3 gap-3 p-4;
  }

  .service-card {
    @apply card-compact flex flex-col items-center justify-center aspect-square hover:shadow-lg transition-shadow duration-200 active:scale-95;
  }

  /* Wallet balance card */
  .wallet-card {
    @apply bg-gradient-to-br from-primary-600 to-primary-800 text-white rounded-2xl p-6 shadow-xl;
  }

  /* Loading states */
  .loading {
    @apply animate-pulse;
  }

  .skeleton {
    @apply bg-gray-200 rounded animate-pulse;
  }

  /* Mobile-specific utilities */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .touch-manipulation {
    touch-action: manipulation;
  }
}
