import axios from 'axios';

// Base API configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://api.bellcollect.com';
const API_PREFIX = import.meta.env.VITE_API_PREFIX || 'app/v1';

// Create axios instance
const api = axios.create({
  baseURL: `${API_BASE_URL}/${API_PREFIX}`,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  timeout: 30000, // 30 seconds timeout
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API endpoints
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  logout: () => api.post('/auth/logout'),
  refreshToken: () => api.post('/auth/refresh'),
  forgotPassword: (email) => api.post('/auth/forgot-password', { email }),
  resetPassword: (data) => api.post('/auth/reset-password', data),
  verifyEmail: (token) => api.post('/auth/verify-email', { token }),
  resendVerification: () => api.post('/auth/resend-verification'),
};

// User API endpoints
export const userAPI = {
  getProfile: () => api.get('/user/profile'),
  updateProfile: (data) => api.put('/user/profile', data),
  changePassword: (data) => api.put('/user/change-password', data),
  uploadAvatar: (formData) => api.post('/user/avatar', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
};

// Wallet API endpoints
export const walletAPI = {
  getBalance: () => api.get('/wallet/balance'),
  getTransactions: (params) => api.get('/wallet/transactions', { params }),
  fundWallet: (data) => api.post('/wallet/fund', data),
  withdrawFunds: (data) => api.post('/wallet/withdraw', data),
};

// Bills API endpoints
export const billsAPI = {
  // Airtime
  buyAirtime: (data) => api.post('/bills/airtime', data),
  getAirtimeProviders: () => api.get('/bills/airtime/providers'),
  
  // Data
  buyData: (data) => api.post('/bills/data', data),
  getDataPlans: (provider) => api.get(`/bills/data/plans/${provider}`),
  
  // TV Subscription
  payTVSubscription: (data) => api.post('/bills/tv', data),
  getTVProviders: () => api.get('/bills/tv/providers'),
  getTVPlans: (provider) => api.get(`/bills/tv/plans/${provider}`),
  
  // Electricity
  payElectricity: (data) => api.post('/bills/electricity', data),
  getElectricityProviders: () => api.get('/bills/electricity/providers'),
  verifyMeterNumber: (provider, meterNumber) => 
    api.post('/bills/electricity/verify', { provider, meter_number: meterNumber }),
  
  // Education
  payEducation: (data) => api.post('/bills/education', data),
  getEducationProviders: () => api.get('/bills/education/providers'),
  
  // Insurance
  payInsurance: (data) => api.post('/bills/insurance', data),
  getInsuranceProviders: () => api.get('/bills/insurance/providers'),
  
  // General bill payment
  payBill: (data) => api.post('/bills/pay', data),
  getBillHistory: (params) => api.get('/bills/history', { params }),
  getBillCategories: () => api.get('/bills/categories'),
};

// Teams API endpoints
export const teamsAPI = {
  getTeams: () => api.get('/teams'),
  createTeam: (data) => api.post('/teams', data),
  getTeam: (teamId) => api.get(`/teams/${teamId}`),
  updateTeam: (teamId, data) => api.put(`/teams/${teamId}`, data),
  deleteTeam: (teamId) => api.delete(`/teams/${teamId}`),
  
  // Team members
  getTeamMembers: (teamId) => api.get(`/teams/${teamId}/members`),
  addTeamMember: (teamId, data) => api.post(`/teams/${teamId}/members`, data),
  updateTeamMember: (teamId, memberId, data) => 
    api.put(`/teams/${teamId}/members/${memberId}`, data),
  removeTeamMember: (teamId, memberId) => 
    api.delete(`/teams/${teamId}/members/${memberId}`),
};

// Rewards API endpoints
export const rewardsAPI = {
  getRewards: () => api.get('/rewards'),
  claimReward: (rewardId) => api.post(`/rewards/${rewardId}/claim`),
  getReferrals: () => api.get('/rewards/referrals'),
  getCashback: () => api.get('/rewards/cashback'),
};

// Notifications API endpoints
export const notificationsAPI = {
  getNotifications: (params) => api.get('/notifications', { params }),
  markAsRead: (notificationId) => api.put(`/notifications/${notificationId}/read`),
  markAllAsRead: () => api.put('/notifications/read-all'),
  deleteNotification: (notificationId) => api.delete(`/notifications/${notificationId}`),
};

export default api;
