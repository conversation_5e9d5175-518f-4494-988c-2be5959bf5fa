import { create } from 'zustand';
import { walletAPI } from '../services/api';
import toast from 'react-hot-toast';

const useWalletStore = create((set, get) => ({
  // State
  balance: 0,
  transactions: [],
  isLoading: false,
  error: null,
  lastUpdated: null,

  // Actions
  fetchBalance: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await walletAPI.getBalance();
      const { balance } = response.data;
      
      set({
        balance: parseFloat(balance) || 0,
        isLoading: false,
        lastUpdated: new Date(),
      });
      
      return { success: true, balance };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch balance';
      set({
        isLoading: false,
        error: errorMessage,
      });
      return { success: false, error: errorMessage };
    }
  },

  fetchTransactions: async (params = {}) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await walletAPI.getTransactions(params);
      const { transactions } = response.data;
      
      set({
        transactions: transactions || [],
        isLoading: false,
      });
      
      return { success: true, transactions };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch transactions';
      set({
        isLoading: false,
        error: errorMessage,
      });
      return { success: false, error: errorMessage };
    }
  },

  fundWallet: async (data) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await walletAPI.fundWallet(data);
      const result = response.data;
      
      // Refresh balance after successful funding
      await get().fetchBalance();
      
      set({ isLoading: false });
      toast.success('Wallet funded successfully');
      
      return { success: true, data: result };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to fund wallet';
      set({
        isLoading: false,
        error: errorMessage,
      });
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  withdrawFunds: async (data) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await walletAPI.withdrawFunds(data);
      const result = response.data;
      
      // Refresh balance after successful withdrawal
      await get().fetchBalance();
      
      set({ isLoading: false });
      toast.success('Withdrawal successful');
      
      return { success: true, data: result };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to withdraw funds';
      set({
        isLoading: false,
        error: errorMessage,
      });
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  // Utility functions
  formatBalance: (amount = null) => {
    const balance = amount !== null ? amount : get().balance;
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 2,
    }).format(balance);
  },

  clearError: () => set({ error: null }),

  // Reset store
  reset: () => set({
    balance: 0,
    transactions: [],
    isLoading: false,
    error: null,
    lastUpdated: null,
  }),
}));

export default useWalletStore;
